<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterClient.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client</a> &gt; <span class="el_source">RustyClusterClient.java</span></div><h1>RustyClusterClient.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client;

import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.connection.AsyncConnectionManager;
import org.npci.rustyclient.client.connection.ConnectionManager;
import org.npci.rustyclient.client.connection.OperationType;
import rustycluster.Rustycluster;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Main client for interacting with RustyCluster.
 * This client provides both synchronous and asynchronous operations.
 *
 * Synchronous methods: set(), get(), delete(), hSet(), etc.
 * Asynchronous methods: setAsync(), getAsync(), deleteAsync(), hSetAsync(), etc.
 *
 * Example usage:
 * &lt;pre&gt;
 * RustyClusterClientConfig config = RustyClusterClientConfig.builder()
 *     .addNode(&quot;localhost&quot;, 50051, NodeRole.PRIMARY)
 *     .build();
 *
 * try (RustyClusterClient client = new RustyClusterClient(config)) {
 *     // Synchronous operations
 *     client.set(&quot;key1&quot;, &quot;value1&quot;);
 *     String value = client.get(&quot;key1&quot;);
 *
 *     // Asynchronous operations
 *     CompletableFuture&amp;lt;Boolean&amp;gt; setFuture = client.setAsync(&quot;key2&quot;, &quot;value2&quot;);
 *     CompletableFuture&amp;lt;String&amp;gt; getFuture = client.getAsync(&quot;key2&quot;);
 * }
 * &lt;/pre&gt;
 */
public class RustyClusterClient implements AutoCloseable {
<span class="fc" id="L41">    private static final Logger logger = LoggerFactory.getLogger(RustyClusterClient.class);</span>

    private final RustyClusterClientConfig config;
    private final ConnectionManager connectionManager;
    private final AsyncConnectionManager asyncConnectionManager;
    private final AuthenticationManager authenticationManager;

    /**
     * Create a new RustyClusterClient with the provided configuration.
     *
     * @param config The client configuration
     */
<span class="nc" id="L53">    public RustyClusterClient(RustyClusterClientConfig config) {</span>
<span class="nc" id="L54">        this.config = config;</span>
<span class="nc" id="L55">        this.authenticationManager = new AuthenticationManager(config);</span>
<span class="nc" id="L56">        this.connectionManager = new ConnectionManager(config, authenticationManager);</span>
<span class="nc" id="L57">        this.asyncConnectionManager = new AsyncConnectionManager(config, authenticationManager);</span>
<span class="nc" id="L58">        logger.info(&quot;RustyClusterClient initialized with both sync and async capabilities&quot;);</span>
<span class="nc" id="L59">    }</span>

    /**
     * Create a new RustyClusterClient with a custom connection manager (for testing).
     *
     * @param config The client configuration
     * @param connectionManager The connection manager to use
     */
<span class="fc" id="L67">    RustyClusterClient(RustyClusterClientConfig config, ConnectionManager connectionManager) {</span>
<span class="fc" id="L68">        this.config = config;</span>
<span class="fc" id="L69">        this.authenticationManager = new AuthenticationManager(config);</span>
<span class="fc" id="L70">        this.connectionManager = connectionManager;</span>
<span class="fc" id="L71">        this.asyncConnectionManager = new AsyncConnectionManager(config, authenticationManager);</span>
<span class="fc" id="L72">        logger.info(&quot;RustyClusterClient initialized with custom connection manager&quot;);</span>
<span class="fc" id="L73">    }</span>

    /**
     * Set a key-value pair.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value, boolean skipReplication) {
<span class="fc" id="L84">        logger.debug(&quot;Setting key: {}&quot;, key);</span>

        try {
<span class="fc" id="L87">            Rustycluster.SetRequest request = Rustycluster.SetRequest.newBuilder()</span>
<span class="fc" id="L88">                    .setKey(key)</span>
<span class="fc" id="L89">                    .setValue(value)</span>
<span class="fc" id="L90">                    .setSkipReplication(skipReplication)</span>
<span class="fc" id="L91">                    .setSkipSiteReplication(false)</span>
<span class="fc" id="L92">                    .build();</span>

<span class="fc" id="L94">            Rustycluster.SetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L95">                    stub.set(request), OperationType.WRITE);</span>

<span class="fc" id="L97">            return response.getSuccess();</span>
<span class="nc" id="L98">        } catch (Exception e) {</span>
<span class="nc" id="L99">            throw e;</span>
        }
    }

    /**
     * Set a key-value pair with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value) {
<span class="fc" id="L111">        return set(key, value, false);</span>
    }

    /**
     * Get a value by key.
     *
     * @param key The key
     * @return The value, or null if not found
     */
    public String get(String key) {
<span class="fc" id="L121">        logger.debug(&quot;Getting key: {}&quot;, key);</span>
<span class="fc" id="L122">        Rustycluster.GetRequest request = Rustycluster.GetRequest.newBuilder()</span>
<span class="fc" id="L123">                .setKey(key)</span>
<span class="fc" id="L124">                .build();</span>

<span class="fc" id="L126">        Rustycluster.GetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L127">                stub.get(request), OperationType.READ);</span>

<span class="fc bfc" id="L129" title="All 2 branches covered.">        return response.getFound() ? response.getValue() : null;</span>
    }

    /**
     * Delete a key.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean delete(String key, boolean skipReplication) {
<span class="fc" id="L140">        logger.debug(&quot;Deleting key: {}&quot;, key);</span>
<span class="fc" id="L141">        Rustycluster.DeleteRequest request = Rustycluster.DeleteRequest.newBuilder()</span>
<span class="fc" id="L142">                .setKey(key)</span>
<span class="fc" id="L143">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L144">                .setSkipSiteReplication(false)</span>
<span class="fc" id="L145">                .build();</span>

<span class="fc" id="L147">        Rustycluster.DeleteResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L148">                stub.delete(request), OperationType.WRITE);</span>

<span class="fc" id="L150">        return response.getSuccess();</span>
    }

    /**
     * Delete a key with default replication.
     *
     * @param key The key
     * @return True if successful, false otherwise
     */
    public boolean delete(String key) {
<span class="fc" id="L160">        return delete(key, false);</span>
    }

    /**
     * Set a key-value pair with expiration.
     *
     * @param key             The key
     * @param value           The value
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl, boolean skipReplication) {
<span class="fc" id="L173">        logger.debug(&quot;Setting key with expiry: {}, ttl: {}&quot;, key, ttl);</span>
<span class="fc" id="L174">        Rustycluster.SetExRequest request = Rustycluster.SetExRequest.newBuilder()</span>
<span class="fc" id="L175">                .setKey(key)</span>
<span class="fc" id="L176">                .setValue(value)</span>
<span class="fc" id="L177">                .setTtl(ttl)</span>
<span class="fc" id="L178">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L179">                .setSkipSiteReplication(false)</span>
<span class="fc" id="L180">                .build();</span>

<span class="fc" id="L182">        Rustycluster.SetExResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L183">                stub.setEx(request), OperationType.WRITE);</span>

<span class="fc" id="L185">        return response.getSuccess();</span>
    }

    /**
     * Set a key-value pair with expiration and default replication.
     *
     * @param key   The key
     * @param value The value
     * @param ttl   The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl) {
<span class="fc" id="L197">        return setEx(key, value, ttl, false);</span>
    }

    /**
     * Set expiration on an existing key.
     *
     * @param key             The key
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl, boolean skipReplication) {
<span class="nc" id="L209">        logger.debug(&quot;Setting expiry on key: {}, ttl: {}&quot;, key, ttl);</span>
<span class="nc" id="L210">        rustycluster.Rustycluster.SetExpiryRequest request = rustycluster.Rustycluster.SetExpiryRequest.newBuilder()</span>
<span class="nc" id="L211">                .setKey(key)</span>
<span class="nc" id="L212">                .setTtl(ttl)</span>
<span class="nc" id="L213">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L214">                .build();</span>

<span class="nc" id="L216">        rustycluster.Rustycluster.SetExpiryResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L217">                stub.setExpiry(request), OperationType.WRITE);</span>

<span class="nc" id="L219">        return response.getSuccess();</span>
    }

    /**
     * Set expiration on an existing key with default replication.
     *
     * @param key The key
     * @param ttl The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl) {
<span class="nc" id="L230">        return setExpiry(key, ttl, false);</span>
    }

    /**
     * Increment a numeric value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long incrBy(String key, long value, boolean skipReplication) {
<span class="fc" id="L242">        logger.debug(&quot;Incrementing key: {} by {}&quot;, key, value);</span>
<span class="fc" id="L243">        rustycluster.Rustycluster.IncrByRequest request = rustycluster.Rustycluster.IncrByRequest.newBuilder()</span>
<span class="fc" id="L244">                .setKey(key)</span>
<span class="fc" id="L245">                .setValue(value)</span>
<span class="fc" id="L246">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L247">                .build();</span>

<span class="fc" id="L249">        rustycluster.Rustycluster.IncrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L250">                stub.incrBy(request), OperationType.WRITE);</span>

<span class="fc" id="L252">        return response.getNewValue();</span>
    }

    /**
     * Increment a numeric value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public long incrBy(String key, long value) {
<span class="fc" id="L263">        return incrBy(key, value, false);</span>
    }

    /**
     * Decrement a numeric value.
     *
     * @param key             The key
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long decrBy(String key, long value, boolean skipReplication) {
<span class="nc" id="L275">        logger.debug(&quot;Decrementing key: {} by {}&quot;, key, value);</span>
<span class="nc" id="L276">        rustycluster.Rustycluster.DecrByRequest request = rustycluster.Rustycluster.DecrByRequest.newBuilder()</span>
<span class="nc" id="L277">                .setKey(key)</span>
<span class="nc" id="L278">                .setValue(value)</span>
<span class="nc" id="L279">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L280">                .build();</span>

<span class="nc" id="L282">        rustycluster.Rustycluster.DecrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L283">                stub.decrBy(request), OperationType.WRITE);</span>

<span class="nc" id="L285">        return response.getNewValue();</span>
    }

    /**
     * Decrement a numeric value with default replication.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The new value
     */
    public long decrBy(String key, long value) {
<span class="nc" id="L296">        return decrBy(key, value, false);</span>
    }

    /**
     * Increment a floating-point value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double incrByFloat(String key, double value, boolean skipReplication) {
<span class="nc" id="L308">        logger.debug(&quot;Incrementing key: {} by float {}&quot;, key, value);</span>
<span class="nc" id="L309">        rustycluster.Rustycluster.IncrByFloatRequest request = rustycluster.Rustycluster.IncrByFloatRequest.newBuilder()</span>
<span class="nc" id="L310">                .setKey(key)</span>
<span class="nc" id="L311">                .setValue(value)</span>
<span class="nc" id="L312">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L313">                .build();</span>

<span class="nc" id="L315">        rustycluster.Rustycluster.IncrByFloatResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L316">                stub.incrByFloat(request), OperationType.WRITE);</span>

<span class="nc" id="L318">        return response.getNewValue();</span>
    }

    /**
     * Increment a floating-point value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public double incrByFloat(String key, double value) {
<span class="nc" id="L329">        return incrByFloat(key, value, false);</span>
    }

    /**
     * Set a field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The field value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value, boolean skipReplication) {
<span class="nc" id="L342">        logger.debug(&quot;Setting hash field: {}.{}&quot;, key, field);</span>
<span class="nc" id="L343">        rustycluster.Rustycluster.HSetRequest request = rustycluster.Rustycluster.HSetRequest.newBuilder()</span>
<span class="nc" id="L344">                .setKey(key)</span>
<span class="nc" id="L345">                .setField(field)</span>
<span class="nc" id="L346">                .setValue(value)</span>
<span class="nc" id="L347">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L348">                .setSkipSiteReplication(false)</span>
<span class="nc" id="L349">                .build();</span>

<span class="nc" id="L351">        rustycluster.Rustycluster.HSetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L352">                stub.hSet(request), OperationType.WRITE);</span>

<span class="nc" id="L354">        return response.getSuccess();</span>
    }

    /**
     * Set a field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value) {
<span class="nc" id="L366">        return hSet(key, field, value, false);</span>
    }

    /**
     * Get a field from a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @return The field value, or null if not found
     */
    public String hGet(String key, String field) {
<span class="nc" id="L377">        logger.debug(&quot;Getting hash field: {}.{}&quot;, key, field);</span>
<span class="nc" id="L378">        rustycluster.Rustycluster.HGetRequest request = rustycluster.Rustycluster.HGetRequest.newBuilder()</span>
<span class="nc" id="L379">                .setKey(key)</span>
<span class="nc" id="L380">                .setField(field)</span>
<span class="nc" id="L381">                .build();</span>

<span class="nc" id="L383">        rustycluster.Rustycluster.HGetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L384">                stub.hGet(request), OperationType.READ);</span>

<span class="nc bnc" id="L386" title="All 2 branches missed.">        return response.getFound() ? response.getValue() : null;</span>
    }

    /**
     * Get all fields from a hash.
     *
     * @param key The hash key
     * @return A map of field names to values
     */
    public Map&lt;String, String&gt; hGetAll(String key) {
<span class="fc" id="L396">        logger.debug(&quot;Getting all hash fields for key: {}&quot;, key);</span>
<span class="fc" id="L397">        rustycluster.Rustycluster.HGetAllRequest request = rustycluster.Rustycluster.HGetAllRequest.newBuilder()</span>
<span class="fc" id="L398">                .setKey(key)</span>
<span class="fc" id="L399">                .build();</span>

<span class="fc" id="L401">        rustycluster.Rustycluster.HGetAllResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L402">                stub.hGetAll(request), OperationType.READ);</span>

<span class="fc" id="L404">        return response.getFieldsMap();</span>
    }

    /**
     * Increment a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value, boolean skipReplication) {
<span class="nc" id="L417">        logger.debug(&quot;Incrementing hash field: {}.{} by {}&quot;, key, field, value);</span>
<span class="nc" id="L418">        rustycluster.Rustycluster.HIncrByRequest request = rustycluster.Rustycluster.HIncrByRequest.newBuilder()</span>
<span class="nc" id="L419">                .setKey(key)</span>
<span class="nc" id="L420">                .setField(field)</span>
<span class="nc" id="L421">                .setValue(value)</span>
<span class="nc" id="L422">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L423">                .setSkipSiteReplication(false)</span>
<span class="nc" id="L424">                .build();</span>

<span class="nc" id="L426">        rustycluster.Rustycluster.HIncrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L427">                stub.hIncrBy(request), OperationType.WRITE);</span>

<span class="nc" id="L429">        return response.getNewValue();</span>
    }

    /**
     * Increment a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value) {
<span class="nc" id="L441">        return hIncrBy(key, field, value, false);</span>
    }

    /**
     * Decrement a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value, boolean skipReplication) {
<span class="nc" id="L454">        logger.debug(&quot;Decrementing hash field: {}.{} by {}&quot;, key, field, value);</span>
<span class="nc" id="L455">        rustycluster.Rustycluster.HDecrByRequest request = rustycluster.Rustycluster.HDecrByRequest.newBuilder()</span>
<span class="nc" id="L456">                .setKey(key)</span>
<span class="nc" id="L457">                .setField(field)</span>
<span class="nc" id="L458">                .setValue(value)</span>
<span class="nc" id="L459">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L460">                .setSkipSiteReplication(false)</span>
<span class="nc" id="L461">                .build();</span>

<span class="nc" id="L463">        rustycluster.Rustycluster.HDecrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L464">                stub.hDecrBy(request), OperationType.WRITE);</span>

<span class="nc" id="L466">        return response.getNewValue();</span>
    }

    /**
     * Decrement a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The decrement value
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value) {
<span class="nc" id="L478">        return hDecrBy(key, field, value, false);</span>
    }

    /**
     * Increment a floating-point field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value, boolean skipReplication) {
<span class="nc" id="L491">        logger.debug(&quot;Incrementing hash field: {}.{} by float {}&quot;, key, field, value);</span>
<span class="nc" id="L492">        rustycluster.Rustycluster.HIncrByFloatRequest request = rustycluster.Rustycluster.HIncrByFloatRequest.newBuilder()</span>
<span class="nc" id="L493">                .setKey(key)</span>
<span class="nc" id="L494">                .setField(field)</span>
<span class="nc" id="L495">                .setValue(value)</span>
<span class="nc" id="L496">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L497">                .build();</span>

<span class="nc" id="L499">        rustycluster.Rustycluster.HIncrByFloatResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L500">                stub.hIncrByFloat(request), OperationType.WRITE);</span>

<span class="nc" id="L502">        return response.getNewValue();</span>
    }

    /**
     * Increment a floating-point field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value) {
<span class="nc" id="L514">        return hIncrByFloat(key, field, value, false);</span>
    }

    /**
     * Execute a batch of operations.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return A list of results, one for each operation
     */
    public List&lt;Boolean&gt; batchWrite(List&lt;rustycluster.Rustycluster.BatchOperation&gt; operations, boolean skipReplication) {
<span class="fc" id="L525">        logger.debug(&quot;Executing batch write with {} operations&quot;, operations.size());</span>
<span class="fc" id="L526">        rustycluster.Rustycluster.BatchWriteRequest request = rustycluster.Rustycluster.BatchWriteRequest.newBuilder()</span>
<span class="fc" id="L527">                .addAllOperations(operations)</span>
<span class="fc" id="L528">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L529">                .build();</span>

<span class="fc" id="L531">        rustycluster.Rustycluster.BatchWriteResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L532">                stub.batchWrite(request), OperationType.WRITE);</span>

<span class="fc" id="L534">        return response.getOperationResultsList();</span>
    }

    /**
     * Execute a batch of operations with default replication.
     *
     * @param operations The list of operations to execute
     * @return A list of results, one for each operation
     */
    public List&lt;Boolean&gt; batchWrite(List&lt;rustycluster.Rustycluster.BatchOperation&gt; operations) {
<span class="fc" id="L544">        return batchWrite(operations, false);</span>
    }

    /**
     * Authenticate with the RustyCluster server.
     * This method should be called before performing any operations if authentication is configured.
     *
     * @return True if authentication was successful, false otherwise
     */
    public boolean authenticate() {
<span class="nc" id="L554">        logger.debug(&quot;Attempting to authenticate with RustyCluster server&quot;);</span>

<span class="nc bnc" id="L556" title="All 2 branches missed.">        if (!config.hasAuthentication()) {</span>
<span class="nc" id="L557">            logger.debug(&quot;No authentication credentials configured&quot;);</span>
<span class="nc" id="L558">            return true;</span>
        }

        try {
            // Get a stub from the connection manager and authenticate
<span class="nc" id="L563">            return connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L564">                authenticationManager.authenticate(stub), OperationType.AUTH);</span>
<span class="nc" id="L565">        } catch (Exception e) {</span>
<span class="nc" id="L566">            logger.error(&quot;Authentication failed&quot;, e);</span>
<span class="nc" id="L567">            return false;</span>
        }
    }

    /**
     * Check if the client is currently authenticated.
     *
     * @return True if authenticated, false otherwise
     */
    public boolean isAuthenticated() {
<span class="nc" id="L577">        return authenticationManager.isAuthenticated();</span>
    }

    /**
     * Get the current session token.
     *
     * @return The session token, or null if not authenticated
     */
    public String getSessionToken() {
<span class="nc" id="L586">        return authenticationManager.getSessionToken();</span>
    }

    // ==================== NEW METHODS ====================

    /**
     * Set multiple fields in a hash.
     *
     * @param key             The hash key
     * @param fields          Map of field-value pairs
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean hMSet(String key, Map&lt;String, String&gt; fields, boolean skipReplication) {
<span class="nc" id="L600">        logger.debug(&quot;Setting multiple hash fields for key: {}, fields count: {}&quot;, key, fields.size());</span>

        try {
<span class="nc" id="L603">            rustycluster.Rustycluster.HMSetRequest request = rustycluster.Rustycluster.HMSetRequest.newBuilder()</span>
<span class="nc" id="L604">                    .setKey(key)</span>
<span class="nc" id="L605">                    .putAllFields(fields)</span>
<span class="nc" id="L606">                    .setSkipReplication(skipReplication)</span>
<span class="nc" id="L607">                    .setSkipSiteReplication(false)</span>
<span class="nc" id="L608">                    .build();</span>

<span class="nc" id="L610">            rustycluster.Rustycluster.HMSetResponse response = connectionManager.executeWithFailover(</span>
<span class="nc" id="L611">                    stub -&gt; stub.hMSet(request), OperationType.WRITE</span>
            );

<span class="nc" id="L614">            return response.getSuccess();</span>
<span class="nc" id="L615">        } catch (Exception e) {</span>
<span class="nc" id="L616">            logger.error(&quot;Error setting multiple hash fields for key: {}&quot;, key, e);</span>
<span class="nc" id="L617">            return false;</span>
        }
    }

    /**
     * Set multiple fields in a hash with default replication.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return True if successful, false otherwise
     */
    public boolean hMSet(String key, Map&lt;String, String&gt; fields) {
<span class="nc" id="L629">        return hMSet(key, fields, false);</span>
    }

    /**
     * Check if a key exists.
     *
     * @param key The key to check
     * @return True if the key exists, false otherwise
     */
    public boolean exists(String key) {
<span class="nc" id="L639">        logger.debug(&quot;Checking if key exists: {}&quot;, key);</span>

        try {
<span class="nc" id="L642">            rustycluster.Rustycluster.ExistsRequest request = rustycluster.Rustycluster.ExistsRequest.newBuilder()</span>
<span class="nc" id="L643">                    .setKey(key)</span>
<span class="nc" id="L644">                    .build();</span>

<span class="nc" id="L646">            rustycluster.Rustycluster.ExistsResponse response = connectionManager.executeWithFailover(</span>
<span class="nc" id="L647">                    stub -&gt; stub.exists(request), OperationType.READ</span>
            );

<span class="nc" id="L650">            return response.getExists();</span>
<span class="nc" id="L651">        } catch (Exception e) {</span>
<span class="nc" id="L652">            logger.error(&quot;Error checking if key exists: {}&quot;, key, e);</span>
<span class="nc" id="L653">            return false;</span>
        }
    }

    /**
     * Check if a hash field exists.
     *
     * @param key   The hash key
     * @param field The field name
     * @return True if field exists, false otherwise
     */
    public boolean hExists(String key, String field) {
<span class="nc" id="L665">        logger.debug(&quot;Checking if hash field exists: {}.{}&quot;, key, field);</span>

        try {
<span class="nc" id="L668">            rustycluster.Rustycluster.HExistsRequest request = rustycluster.Rustycluster.HExistsRequest.newBuilder()</span>
<span class="nc" id="L669">                    .setKey(key)</span>
<span class="nc" id="L670">                    .setField(field)</span>
<span class="nc" id="L671">                    .build();</span>

<span class="nc" id="L673">            rustycluster.Rustycluster.HExistsResponse response = connectionManager.executeWithFailover(</span>
<span class="nc" id="L674">                    stub -&gt; stub.hExists(request), OperationType.READ</span>
            );

<span class="nc" id="L677">            return response.getExists();</span>
<span class="nc" id="L678">        } catch (Exception e) {</span>
<span class="nc" id="L679">            logger.error(&quot;Error checking if hash field exists: {}.{}&quot;, key, field, e);</span>
<span class="nc" id="L680">            return false;</span>
        }
    }

    /**
     * Set a key-value pair only if the key does not exist.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return True if key was set, false if key already existed
     */
    public boolean setNX(String key, String value, boolean skipReplication) {
<span class="nc" id="L693">        logger.debug(&quot;Setting key if not exists: {}&quot;, key);</span>

        try {
<span class="nc" id="L696">            rustycluster.Rustycluster.SetNXRequest request = rustycluster.Rustycluster.SetNXRequest.newBuilder()</span>
<span class="nc" id="L697">                    .setKey(key)</span>
<span class="nc" id="L698">                    .setValue(value)</span>
<span class="nc" id="L699">                    .setSkipReplication(skipReplication)</span>
<span class="nc" id="L700">                    .setSkipSiteReplication(false)</span>
<span class="nc" id="L701">                    .build();</span>

<span class="nc" id="L703">            rustycluster.Rustycluster.SetNXResponse response = connectionManager.executeWithFailover(</span>
<span class="nc" id="L704">                    stub -&gt; stub.setNX(request), OperationType.WRITE</span>
            );

<span class="nc" id="L707">            return response.getSuccess();</span>
<span class="nc" id="L708">        } catch (Exception e) {</span>
<span class="nc" id="L709">            logger.error(&quot;Error setting key if not exists: {}&quot;, key, e);</span>
<span class="nc" id="L710">            return false;</span>
        }
    }

    /**
     * Set a key-value pair only if the key does not exist with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return True if key was set, false if key already existed
     */
    public boolean setNX(String key, String value) {
<span class="nc" id="L722">        return setNX(key, value, false);</span>
    }



    /**
     * Load a Lua script and return its SHA hash.
     *
     * @param script The Lua script to load
     * @return The SHA hash of the loaded script, or null if failed
     */
    public String loadScript(String script) {
<span class="nc" id="L734">        logger.debug(&quot;Loading script, length: {}&quot;, script.length());</span>

        try {
<span class="nc" id="L737">            rustycluster.Rustycluster.LoadScriptRequest request = rustycluster.Rustycluster.LoadScriptRequest.newBuilder()</span>
<span class="nc" id="L738">                    .setScript(script)</span>
<span class="nc" id="L739">                    .build();</span>

<span class="nc" id="L741">            rustycluster.Rustycluster.LoadScriptResponse response = connectionManager.executeWithFailover(</span>
<span class="nc" id="L742">                    stub -&gt; stub.loadScript(request), OperationType.WRITE</span>
            );

<span class="nc bnc" id="L745" title="All 2 branches missed.">            return response.getSuccess() ? response.getSha() : null;</span>
<span class="nc" id="L746">        } catch (Exception e) {</span>
<span class="nc" id="L747">            logger.error(&quot;Error loading script&quot;, e);</span>
<span class="nc" id="L748">            return null;</span>
        }
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash.
     *
     * @param sha             The SHA hash of the script
     * @param keys            List of keys to pass to the script
     * @param args            List of arguments to pass to the script
     * @param skipReplication Whether to skip replication
     * @return The script execution result, or null if failed
     */
    public String evalSha(String sha, List&lt;String&gt; keys, List&lt;String&gt; args, boolean skipReplication) {
<span class="nc" id="L762">        logger.debug(&quot;Executing script with SHA: {}, keys: {}, args: {}&quot;, sha, keys.size(), args.size());</span>

        try {
<span class="nc" id="L765">            rustycluster.Rustycluster.EvalShaRequest request = rustycluster.Rustycluster.EvalShaRequest.newBuilder()</span>
<span class="nc" id="L766">                    .setSha(sha)</span>
<span class="nc" id="L767">                    .addAllKeys(keys)</span>
<span class="nc" id="L768">                    .addAllArgs(args)</span>
<span class="nc" id="L769">                    .setSkipReplication(skipReplication)</span>
<span class="nc" id="L770">                    .setSkipSiteReplication(false)</span>
<span class="nc" id="L771">                    .build();</span>

<span class="nc" id="L773">            rustycluster.Rustycluster.EvalShaResponse response = connectionManager.executeWithFailover(</span>
<span class="nc" id="L774">                    stub -&gt; stub.evalSha(request), OperationType.WRITE</span>
            );

<span class="nc bnc" id="L777" title="All 2 branches missed.">            return response.getSuccess() ? response.getResult() : null;</span>
<span class="nc" id="L778">        } catch (Exception e) {</span>
<span class="nc" id="L779">            logger.error(&quot;Error executing script with SHA: {}&quot;, sha, e);</span>
<span class="nc" id="L780">            return null;</span>
        }
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash with default replication.
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @param args List of arguments to pass to the script
     * @return The script execution result, or null if failed
     */
    public String evalSha(String sha, List&lt;String&gt; keys, List&lt;String&gt; args) {
<span class="nc" id="L793">        return evalSha(sha, keys, args, false);</span>
    }

    /**
     * Perform a health check on the cluster.
     *
     * @return True if healthy, false otherwise
     */
    public boolean healthCheck() {
<span class="nc" id="L802">        logger.debug(&quot;Performing health check&quot;);</span>

        // Use ping for now until HealthCheck RPC is available
        try {
<span class="nc" id="L806">            return ping();</span>
<span class="nc" id="L807">        } catch (Exception e) {</span>
<span class="nc" id="L808">            logger.warn(&quot;Health check failed: {}&quot;, e.getMessage());</span>
<span class="nc" id="L809">            return false;</span>
        }
    }

    /**
     * Ping the cluster to check connectivity.
     *
     * @return True if ping successful, false otherwise
     */
    public boolean ping() {
<span class="nc" id="L819">        logger.debug(&quot;Pinging cluster&quot;);</span>

        try {
<span class="nc" id="L822">            rustycluster.Rustycluster.PingRequest request = rustycluster.Rustycluster.PingRequest.newBuilder().build();</span>
<span class="nc" id="L823">            rustycluster.Rustycluster.PingResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L824">                    stub.ping(request), OperationType.READ);</span>
<span class="nc" id="L825">            return response.getSuccess();</span>
<span class="nc" id="L826">        } catch (Exception e) {</span>
<span class="nc" id="L827">            logger.warn(&quot;Ping failed: {}&quot;, e.getMessage());</span>
<span class="nc" id="L828">            return false;</span>
        }
    }

    // ==================== ASYNCHRONOUS METHODS ====================

    /**
     * Set a key-value pair asynchronously.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; setAsync(String key, String value, boolean skipReplication) {
<span class="nc" id="L843">        logger.debug(&quot;Setting key asynchronously: {}&quot;, key);</span>
<span class="nc" id="L844">        rustycluster.Rustycluster.SetRequest request = rustycluster.Rustycluster.SetRequest.newBuilder()</span>
<span class="nc" id="L845">                .setKey(key)</span>
<span class="nc" id="L846">                .setValue(value)</span>
<span class="nc" id="L847">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L848">                .build();</span>

<span class="nc" id="L850">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L851">                stub.set(request))</span>
<span class="nc" id="L852">                .thenApply(rustycluster.Rustycluster.SetResponse::getSuccess);</span>
    }

    /**
     * Set a key-value pair asynchronously with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; setAsync(String key, String value) {
<span class="nc" id="L863">        return setAsync(key, value, false);</span>
    }

    /**
     * Get a value by key asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with the value, or null if not found
     */
    public CompletableFuture&lt;String&gt; getAsync(String key) {
<span class="nc" id="L873">        logger.debug(&quot;Getting key asynchronously: {}&quot;, key);</span>
<span class="nc" id="L874">        rustycluster.Rustycluster.GetRequest request = rustycluster.Rustycluster.GetRequest.newBuilder()</span>
<span class="nc" id="L875">                .setKey(key)</span>
<span class="nc" id="L876">                .build();</span>

<span class="nc" id="L878">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L879">                stub.get(request))</span>
<span class="nc bnc" id="L880" title="All 2 branches missed.">                .thenApply(response -&gt; response.getFound() ? response.getValue() : null);</span>
    }

    /**
     * Delete a key asynchronously.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; deleteAsync(String key, boolean skipReplication) {
<span class="nc" id="L891">        logger.debug(&quot;Deleting key asynchronously: {}&quot;, key);</span>
<span class="nc" id="L892">        rustycluster.Rustycluster.DeleteRequest request = rustycluster.Rustycluster.DeleteRequest.newBuilder()</span>
<span class="nc" id="L893">                .setKey(key)</span>
<span class="nc" id="L894">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L895">                .build();</span>

<span class="nc" id="L897">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L898">                stub.delete(request))</span>
<span class="nc" id="L899">                .thenApply(rustycluster.Rustycluster.DeleteResponse::getSuccess);</span>
    }

    /**
     * Delete a key asynchronously with default replication.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; deleteAsync(String key) {
<span class="nc" id="L909">        return deleteAsync(key, false);</span>
    }

    /**
     * Execute a batch of operations asynchronously.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture&lt;List&lt;Boolean&gt;&gt; batchWriteAsync(List&lt;rustycluster.Rustycluster.BatchOperation&gt; operations, boolean skipReplication) {
<span class="nc" id="L920">        logger.debug(&quot;Executing batch write asynchronously with {} operations&quot;, operations.size());</span>
<span class="nc" id="L921">        rustycluster.Rustycluster.BatchWriteRequest request = rustycluster.Rustycluster.BatchWriteRequest.newBuilder()</span>
<span class="nc" id="L922">                .addAllOperations(operations)</span>
<span class="nc" id="L923">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L924">                .build();</span>

<span class="nc" id="L926">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L927">                stub.batchWrite(request))</span>
<span class="nc" id="L928">                .thenApply(rustycluster.Rustycluster.BatchWriteResponse::getOperationResultsList);</span>
    }

    /**
     * Execute a batch of operations asynchronously with default replication.
     *
     * @param operations The list of operations to execute
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture&lt;List&lt;Boolean&gt;&gt; batchWriteAsync(List&lt;rustycluster.Rustycluster.BatchOperation&gt; operations) {
<span class="nc" id="L938">        return batchWriteAsync(operations, false);</span>
    }

    /**
     * Increment a numeric value asynchronously.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture&lt;Long&gt; incrByAsync(String key, long value, boolean skipReplication) {
<span class="nc" id="L950">        logger.debug(&quot;Incrementing key asynchronously: {} by {}&quot;, key, value);</span>
<span class="nc" id="L951">        rustycluster.Rustycluster.IncrByRequest request = rustycluster.Rustycluster.IncrByRequest.newBuilder()</span>
<span class="nc" id="L952">                .setKey(key)</span>
<span class="nc" id="L953">                .setValue(value)</span>
<span class="nc" id="L954">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L955">                .build();</span>

<span class="nc" id="L957">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L958">                stub.incrBy(request))</span>
<span class="nc" id="L959">                .thenApply(rustycluster.Rustycluster.IncrByResponse::getNewValue);</span>
    }

    /**
     * Increment a numeric value asynchronously with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture&lt;Long&gt; incrByAsync(String key, long value) {
<span class="nc" id="L970">        return incrByAsync(key, value, false);</span>
    }

    /**
     * Set a hash field asynchronously.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The field value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hSetAsync(String key, String field, String value, boolean skipReplication) {
<span class="nc" id="L983">        logger.debug(&quot;Setting hash field asynchronously: {}.{}&quot;, key, field);</span>
<span class="nc" id="L984">        rustycluster.Rustycluster.HSetRequest request = rustycluster.Rustycluster.HSetRequest.newBuilder()</span>
<span class="nc" id="L985">                .setKey(key)</span>
<span class="nc" id="L986">                .setField(field)</span>
<span class="nc" id="L987">                .setValue(value)</span>
<span class="nc" id="L988">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L989">                .build();</span>

<span class="nc" id="L991">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L992">                stub.hSet(request))</span>
<span class="nc" id="L993">                .thenApply(rustycluster.Rustycluster.HSetResponse::getSuccess);</span>
    }

    /**
     * Set a hash field asynchronously with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hSetAsync(String key, String field, String value) {
<span class="nc" id="L1005">        return hSetAsync(key, field, value, false);</span>
    }

    /**
     * Get a hash field asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with the field value, or null if not found
     */
    public CompletableFuture&lt;String&gt; hGetAsync(String key, String field) {
<span class="nc" id="L1016">        logger.debug(&quot;Getting hash field asynchronously: {}.{}&quot;, key, field);</span>
<span class="nc" id="L1017">        rustycluster.Rustycluster.HGetRequest request = rustycluster.Rustycluster.HGetRequest.newBuilder()</span>
<span class="nc" id="L1018">                .setKey(key)</span>
<span class="nc" id="L1019">                .setField(field)</span>
<span class="nc" id="L1020">                .build();</span>

<span class="nc" id="L1022">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L1023">                stub.hGet(request))</span>
<span class="nc bnc" id="L1024" title="All 2 branches missed.">                .thenApply(response -&gt; response.getFound() ? response.getValue() : null);</span>
    }

    /**
     * Get all fields from a hash asynchronously.
     *
     * @param key The hash key
     * @return CompletableFuture that completes with a map of field names to values
     */
    public CompletableFuture&lt;Map&lt;String, String&gt;&gt; hGetAllAsync(String key) {
<span class="nc" id="L1034">        logger.debug(&quot;Getting all hash fields asynchronously for key: {}&quot;, key);</span>
<span class="nc" id="L1035">        rustycluster.Rustycluster.HGetAllRequest request = rustycluster.Rustycluster.HGetAllRequest.newBuilder()</span>
<span class="nc" id="L1036">                .setKey(key)</span>
<span class="nc" id="L1037">                .build();</span>

<span class="nc" id="L1039">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L1040">                stub.hGetAll(request))</span>
<span class="nc" id="L1041">                .thenApply(rustycluster.Rustycluster.HGetAllResponse::getFieldsMap);</span>
    }

    // ==================== NEW ASYNC METHODS ====================

    /**
     * Set multiple fields in a hash asynchronously.
     *
     * @param key             The hash key
     * @param fields          Map of field-value pairs
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hMSetAsync(String key, Map&lt;String, String&gt; fields, boolean skipReplication) {
<span class="nc" id="L1055">        logger.debug(&quot;Setting multiple hash fields asynchronously for key: {}, fields count: {}&quot;, key, fields.size());</span>

        // For now, we'll implement this using individual hSet calls until gRPC classes are regenerated
        // This is a temporary implementation
<span class="nc" id="L1059">        List&lt;CompletableFuture&lt;Boolean&gt;&gt; futures = fields.entrySet().stream()</span>
<span class="nc" id="L1060">                .map(entry -&gt; hSetAsync(key, entry.getKey(), entry.getValue(), skipReplication))</span>
<span class="nc" id="L1061">                .toList();</span>

<span class="nc" id="L1063">        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))</span>
<span class="nc" id="L1064">                .thenApply(v -&gt; futures.stream().allMatch(CompletableFuture::join));</span>
    }

    /**
     * Set multiple fields in a hash asynchronously with default replication.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hMSetAsync(String key, Map&lt;String, String&gt; fields) {
<span class="nc" id="L1075">        return hMSetAsync(key, fields, false);</span>
    }

    /**
     * Check if a hash field exists asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with true if field exists, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hExistsAsync(String key, String field) {
<span class="nc" id="L1086">        logger.debug(&quot;Checking if hash field exists asynchronously: {}.{}&quot;, key, field);</span>

        // Temporary implementation using hGet until gRPC classes are regenerated
<span class="nc bnc" id="L1089" title="All 2 branches missed.">        return hGetAsync(key, field).thenApply(value -&gt; value != null);</span>
    }

    /**
     * Check if a key exists asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if key exists, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; existsAsync(String key) {
<span class="nc" id="L1099">        logger.debug(&quot;Checking if key exists asynchronously: {}&quot;, key);</span>

        try {
<span class="nc" id="L1102">            rustycluster.Rustycluster.ExistsRequest request = rustycluster.Rustycluster.ExistsRequest.newBuilder()</span>
<span class="nc" id="L1103">                    .setKey(key)</span>
<span class="nc" id="L1104">                    .build();</span>

<span class="nc" id="L1106">            return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L1107">                    stub.exists(request))</span>
<span class="nc" id="L1108">                    .thenApply(rustycluster.Rustycluster.ExistsResponse::getExists)</span>
<span class="nc" id="L1109">                    .exceptionally(throwable -&gt; {</span>
<span class="nc" id="L1110">                        logger.error(&quot;Error checking if key exists asynchronously: {}&quot;, key, throwable);</span>
<span class="nc" id="L1111">                        return false;</span>
                    });
<span class="nc" id="L1113">        } catch (Exception e) {</span>
<span class="nc" id="L1114">            logger.error(&quot;Error checking if key exists asynchronously: {}&quot;, key, e);</span>
<span class="nc" id="L1115">            return CompletableFuture.completedFuture(false);</span>
        }
    }

    /**
     * Set a key-value pair only if the key does not exist asynchronously.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if key was set, false if key already existed
     */
    public CompletableFuture&lt;Boolean&gt; setNXAsync(String key, String value, boolean skipReplication) {
<span class="nc" id="L1128">        logger.debug(&quot;Setting key if not exists asynchronously: {}&quot;, key);</span>

        // Temporary implementation using exists check + set until gRPC classes are regenerated
<span class="nc" id="L1131">        return existsAsync(key).thenCompose(exists -&gt; {</span>
<span class="nc bnc" id="L1132" title="All 2 branches missed.">            if (exists) {</span>
<span class="nc" id="L1133">                return CompletableFuture.completedFuture(false); // Key already exists</span>
            }
<span class="nc" id="L1135">            return setAsync(key, value, skipReplication);</span>
        });
    }

    /**
     * Set a key-value pair only if the key does not exist asynchronously with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if key was set, false if key already existed
     */
    public CompletableFuture&lt;Boolean&gt; setNXAsync(String key, String value) {
<span class="nc" id="L1147">        return setNXAsync(key, value, false);</span>
    }

    /**
     * Load a Lua script and return its SHA hash asynchronously.
     *
     * @param script The Lua script to load
     * @return CompletableFuture that completes with the SHA hash of the loaded script, or null if failed
     */
    public CompletableFuture&lt;String&gt; loadScriptAsync(String script) {
<span class="nc" id="L1157">        logger.debug(&quot;Loading script asynchronously, length: {}&quot;, script.length());</span>

        // Temporary implementation - return a mock SHA until gRPC classes are regenerated
        // In real implementation, this would call the LoadScript RPC
<span class="nc" id="L1161">        logger.warn(&quot;loadScriptAsync is not fully implemented yet - requires gRPC class regeneration&quot;);</span>
<span class="nc" id="L1162">        return CompletableFuture.completedFuture(null);</span>
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash asynchronously.
     *
     * @param sha             The SHA hash of the script
     * @param keys            List of keys to pass to the script
     * @param args            List of arguments to pass to the script
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture&lt;String&gt; evalShaAsync(String sha, List&lt;String&gt; keys, List&lt;String&gt; args, boolean skipReplication) {
<span class="nc" id="L1175">        logger.debug(&quot;Executing script asynchronously with SHA: {}, keys: {}, args: {}&quot;, sha, keys.size(), args.size());</span>

        // Temporary implementation until gRPC classes are regenerated
<span class="nc" id="L1178">        logger.warn(&quot;evalShaAsync is not fully implemented yet - requires gRPC class regeneration&quot;);</span>
<span class="nc" id="L1179">        return CompletableFuture.completedFuture(null);</span>
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash asynchronously with default replication.
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @param args List of arguments to pass to the script
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture&lt;String&gt; evalShaAsync(String sha, List&lt;String&gt; keys, List&lt;String&gt; args) {
<span class="nc" id="L1191">        return evalShaAsync(sha, keys, args, false);</span>
    }

    /**
     * Perform a health check on the cluster asynchronously.
     *
     * @return CompletableFuture that completes with true if healthy, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; healthCheckAsync() {
<span class="nc" id="L1200">        logger.debug(&quot;Performing health check asynchronously&quot;);</span>

        // Use ping for now until HealthCheck RPC is available
<span class="nc" id="L1203">        return pingAsync().exceptionally(throwable -&gt; {</span>
<span class="nc" id="L1204">            logger.warn(&quot;Health check failed: {}&quot;, throwable.getMessage());</span>
<span class="nc" id="L1205">            return false;</span>
        });
    }

    /**
     * Ping the cluster to check connectivity asynchronously.
     *
     * @return CompletableFuture that completes with true if ping successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; pingAsync() {
<span class="nc" id="L1215">        logger.debug(&quot;Pinging cluster asynchronously&quot;);</span>

<span class="nc" id="L1217">        rustycluster.Rustycluster.PingRequest request = rustycluster.Rustycluster.PingRequest.newBuilder().build();</span>
<span class="nc" id="L1218">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L1219">                stub.ping(request))</span>
<span class="nc" id="L1220">                .thenApply(rustycluster.Rustycluster.PingResponse::getSuccess)</span>
<span class="nc" id="L1221">                .exceptionally(throwable -&gt; {</span>
<span class="nc" id="L1222">                    logger.warn(&quot;Ping failed: {}&quot;, throwable.getMessage());</span>
<span class="nc" id="L1223">                    return false;</span>
                });
    }

    /**
     * Authenticate with the RustyCluster server asynchronously.
     *
     * @return CompletableFuture that completes with true if authentication was successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; authenticateAsync() {
<span class="nc" id="L1233">        logger.debug(&quot;Attempting to authenticate asynchronously with RustyCluster server&quot;);</span>

<span class="nc bnc" id="L1235" title="All 2 branches missed.">        if (!config.hasAuthentication()) {</span>
<span class="nc" id="L1236">            logger.debug(&quot;No authentication credentials configured&quot;);</span>
<span class="nc" id="L1237">            return CompletableFuture.completedFuture(true);</span>
        }

        // For async authentication, we need to use a different approach
        // since authenticate method expects a blocking stub
<span class="nc" id="L1242">        return CompletableFuture.supplyAsync(() -&gt; {</span>
            try {
                // This is a simplified approach - in a real implementation,
                // you'd want to create an async version of authenticate
<span class="nc" id="L1246">                return authenticationManager.isAuthenticated();</span>
<span class="nc" id="L1247">            } catch (Exception e) {</span>
<span class="nc" id="L1248">                logger.error(&quot;Authentication check failed&quot;, e);</span>
<span class="nc" id="L1249">                return false;</span>
            }
        });
    }

    /**
     * Close the client and release all resources.
     */
    @Override
    public void close() {
<span class="fc" id="L1259">        authenticationManager.clearAuthentication();</span>
<span class="fc" id="L1260">        connectionManager.close();</span>
<span class="fc" id="L1261">        asyncConnectionManager.close();</span>
<span class="fc" id="L1262">        logger.info(&quot;RustyClusterClient closed&quot;);</span>
<span class="fc" id="L1263">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>